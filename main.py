from fastapi import Fast<PERSON><PERSON>, HTTPException, status
from pydantic import BaseModel
import sqlite3
from typing import List, Optional
import os

app = FastAPI(title="Feature Voting API", version="1.0.0")

# Pydantic Models
class Feature(BaseModel):
    id: int
    title: str
    votes: int

class FeatureCreate(BaseModel):
    title: str

class FeatureResponse(BaseModel):
    id: int
    title: str
    votes: int

# Database configuration
DATABASE_PATH = "features.db"

def get_db_connection():
    """Get database connection with row factory for dict-like access"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """Initialize the database and create features table"""
    conn = get_db_connection()
    try:
        conn.execute("""
            CREATE TABLE IF NOT EXISTS features (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                votes INTEGER DEFAULT 0
            )
        """)
        conn.commit()
    finally:
        conn.close()

def create_feature(title: str) -> Feature:
    """Insert new feature into database"""
    conn = get_db_connection()
    try:
        cursor = conn.execute(
            "INSERT INTO features (title, votes) VALUES (?, 0)",
            (title,)
        )
        conn.commit()
        feature_id = cursor.lastrowid
        
        # Fetch the created feature
        row = conn.execute(
            "SELECT id, title, votes FROM features WHERE id = ?",
            (feature_id,)
        ).fetchone()
        
        return Feature(id=row["id"], title=row["title"], votes=row["votes"])
    finally:
        conn.close()

def get_features() -> List[Feature]:
    """Return all features ordered by votes DESC"""
    conn = get_db_connection()
    try:
        rows = conn.execute(
            "SELECT id, title, votes FROM features ORDER BY votes DESC, id ASC"
        ).fetchall()
        
        return [Feature(id=row["id"], title=row["title"], votes=row["votes"]) for row in rows]
    finally:
        conn.close()

def get_feature_by_id(feature_id: int) -> Optional[Feature]:
    """Get a single feature by ID"""
    conn = get_db_connection()
    try:
        row = conn.execute(
            "SELECT id, title, votes FROM features WHERE id = ?",
            (feature_id,)
        ).fetchone()
        
        if row:
            return Feature(id=row["id"], title=row["title"], votes=row["votes"])
        return None
    finally:
        conn.close()

def upvote_feature(feature_id: int) -> Optional[Feature]:
    """Increment vote count for a feature"""
    conn = get_db_connection()
    try:
        # Check if feature exists and update votes
        cursor = conn.execute(
            "UPDATE features SET votes = votes + 1 WHERE id = ?",
            (feature_id,)
        )
        
        if cursor.rowcount == 0:
            return None
        
        conn.commit()
        
        # Fetch updated feature
        row = conn.execute(
            "SELECT id, title, votes FROM features WHERE id = ?",
            (feature_id,)
        ).fetchone()
        
        return Feature(id=row["id"], title=row["title"], votes=row["votes"])
    finally:
        conn.close()

def delete_feature(feature_id: int) -> bool:
    """Delete a feature by ID"""
    conn = get_db_connection()
    try:
        cursor = conn.execute("DELETE FROM features WHERE id = ?", (feature_id,))
        conn.commit()
        return cursor.rowcount > 0
    finally:
        conn.close()

# API Endpoints

@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    init_db()

@app.get("/", tags=["Root"])
async def root():
    """Root endpoint"""
    return {"message": "Feature Voting API", "version": "1.0.0"}

@app.post("/features", response_model=FeatureResponse, status_code=status.HTTP_201_CREATED, tags=["Features"])
async def create_feature_endpoint(feature: FeatureCreate):
    """Create a new feature"""
    try:
        new_feature = create_feature(feature.title)
        return FeatureResponse(id=new_feature.id, title=new_feature.title, votes=new_feature.votes)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create feature"
        )

@app.get("/features", response_model=List[FeatureResponse], tags=["Features"])
async def get_features_endpoint():
    """Get all features ordered by votes (descending)"""
    try:
        features = get_features()
        return [FeatureResponse(id=f.id, title=f.title, votes=f.votes) for f in features]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve features"
        )

@app.get("/features/{feature_id}", response_model=FeatureResponse, tags=["Features"])
async def get_feature_endpoint(feature_id: int):
    """Get a specific feature by ID"""
    try:
        feature = get_feature_by_id(feature_id)
        if not feature:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Feature with id {feature_id} not found"
            )
        return FeatureResponse(id=feature.id, title=feature.title, votes=feature.votes)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve feature"
        )

@app.post("/features/{feature_id}/upvote", response_model=FeatureResponse, tags=["Features"])
async def upvote_feature_endpoint(feature_id: int):
    """Upvote a feature (increment vote count)"""
    try:
        updated_feature = upvote_feature(feature_id)
        if not updated_feature:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Feature with id {feature_id} not found"
            )
        return FeatureResponse(id=updated_feature.id, title=updated_feature.title, votes=updated_feature.votes)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upvote feature"
        )

@app.delete("/features/{feature_id}", status_code=status.HTTP_204_NO_CONTENT, tags=["Features"])
async def delete_feature_endpoint(feature_id: int):
    """Delete a feature by ID"""
    try:
        deleted = delete_feature(feature_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Feature with id {feature_id} not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete feature"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
