# Feature Voting API

A FastAPI backend with SQLite database for managing feature requests and voting.

## Features

- Create new feature requests
- List all features (ordered by votes descending)
- Get individual features by ID
- Upvote features
- Delete features
- SQLite database with automatic initialization
- RESTful API design with proper error handling

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the application:
```bash
python main.py
```

Or using uvicorn directly:
```bash
uvicorn main:app --reload
```

The API will be available at `http://localhost:8000`

## API Documentation

Once running, visit:
- Interactive API docs: `http://localhost:8000/docs`
- Alternative docs: `http://localhost:8000/redoc`

## API Endpoints

### GET /
Root endpoint with API information

### POST /features
Create a new feature
```json
{
  "title": "Feature title"
}
```

### GET /features
Get all features (ordered by votes descending)

### GET /features/{feature_id}
Get a specific feature by ID

### POST /features/{feature_id}/upvote
Upvote a feature (increment vote count)

### DELETE /features/{feature_id}
Delete a feature by ID

## Database Schema

The SQLite database contains a single `features` table:

```sql
CREATE TABLE features (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    votes INTEGER DEFAULT 0
);
```

## Error Handling

The API follows REST conventions:
- `200 OK` - Successful GET requests
- `201 Created` - Successful POST requests
- `204 No Content` - Successful DELETE requests
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server errors
